"""
Systemd Service Wrapper for Linux Log Collection Agent

This module provides systemd service functionality for running the
Linux log collection agent as a system service.
"""

import logging
import os
import signal
import sys
import time
from pathlib import Path
from typing import Optional

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from logging_agent.agent import LinuxLoggingAgent
from config.config_manager import ConfigManager


class LinuxLoggingAgentService:
    """Systemd service wrapper for the Linux Logging Agent."""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize the service."""
        self.config_path = config_path
        self.agent = None
        self.logger = None
        self._running = False
        
        # Set up signal handlers
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGHUP, self._reload_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        if self.logger:
            self.logger.info(f"Received signal {signum}, shutting down...")
        
        self._running = False
        
        if self.agent:
            self.agent.stop()
    
    def _reload_handler(self, signum, frame):
        """Handle reload signal (SIGHUP)."""
        if self.logger:
            self.logger.info("Received SIGHUP, reloading configuration...")
        
        if self.agent:
            self.agent.reload_config()
    
    def run(self) -> int:
        """
        Run the service.
        
        Returns:
            Exit code (0 for success, non-zero for error)
        """
        try:
            # Set up logging for service mode
            self._setup_service_logging()
            
            self.logger.info("Linux Log Collection Agent Service starting...")
            
            # Initialize the agent
            self.agent = LinuxLoggingAgent(
                config_path=self.config_path,
                enable_signals=False  # We handle signals in the service
            )
            
            # Start the agent
            if not self.agent.start():
                self.logger.error("Failed to start the logging agent")
                return 1
            
            self.logger.info("Linux Log Collection Agent Service started successfully")
            self._running = True
            
            # Main service loop
            while self._running:
                try:
                    time.sleep(1)
                    
                    # Check if agent is still running
                    if not self.agent._running:
                        self.logger.error("Agent stopped unexpectedly")
                        break
                    
                    # Log status periodically (every 5 minutes)
                    if int(time.time()) % 300 == 0:
                        status = self.agent.get_status()
                        self.logger.info(
                            f"Service status: Uptime {status['uptime_seconds']:.0f}s, "
                            f"Logs collected: {status['statistics']['logs_collected']}, "
                            f"Logs processed: {status['statistics']['logs_processed']}"
                        )
                
                except Exception as e:
                    self.logger.error(f"Error in service loop: {e}")
                    time.sleep(5)
            
            self.logger.info("Linux Log Collection Agent Service stopping...")
            
            if self.agent:
                self.agent.stop()
            
            self.logger.info("Linux Log Collection Agent Service stopped")
            return 0
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Service error: {e}")
            else:
                print(f"Service error: {e}")
            return 1
    
    def _setup_service_logging(self):
        """Set up logging for service mode."""
        # Create log directory
        log_dir = '/var/log/linux-log-agent'
        os.makedirs(log_dir, exist_ok=True)
        
        # Set up basic logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(os.path.join(log_dir, 'service.log')),
                logging.StreamHandler()  # For systemd journal
            ]
        )
        
        self.logger = logging.getLogger('LinuxLogAgentService')


def main():
    """Main entry point for systemd service."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Linux Log Collection Agent Service')
    parser.add_argument('--config', type=str, help='Path to configuration file')
    
    args = parser.parse_args()
    
    service = LinuxLoggingAgentService(config_path=args.config)
    return service.run()


if __name__ == '__main__':
    sys.exit(main())
