#!/usr/bin/env python3

"""
Linux Agent Configuration Checker

This script checks the agent configuration and provides diagnostics
for log collection and API connectivity issues.
"""

import yaml
import os
import json
import subprocess
import sys
from pathlib import Path

def load_config():
    """Load agent configuration."""
    config_paths = [
        "/etc/linux-log-agent/config.yaml",
        "config/default_config.yaml",
        "./config.yaml"
    ]
    
    for config_path in config_paths:
        if os.path.exists(config_path):
            print(f"📄 Found config at: {config_path}")
            try:
                with open(config_path, 'r') as f:
                    return yaml.safe_load(f), config_path
            except Exception as e:
                print(f"❌ Error loading config: {e}")
                continue
    
    print("❌ No configuration file found!")
    return None, None

def check_api_config(config):
    """Check API configuration."""
    print("\n🔗 API Configuration:")
    print("-" * 30)
    
    api_config = config.get('exlog_api', {})
    
    # Check if API is enabled
    enabled = api_config.get('enabled', False)
    print(f"Enabled: {'✅' if enabled else '❌'} {enabled}")
    
    # Check endpoint
    endpoint = api_config.get('endpoint', 'Not set')
    print(f"Endpoint: {endpoint}")
    
    # Check API key
    api_key = api_config.get('api_key', 'Not set')
    if api_key and api_key != 'Not set':
        print(f"API Key: {api_key[:20]}... (length: {len(api_key)})")
    else:
        print("API Key: ❌ Not set")
    
    # Check other settings
    batch_size = api_config.get('batch_size', 100)
    timeout = api_config.get('timeout', 30)
    max_retries = api_config.get('max_retries', 3)
    
    print(f"Batch Size: {batch_size}")
    print(f"Timeout: {timeout}s")
    print(f"Max Retries: {max_retries}")
    
    # Check offline buffer
    offline_buffer = api_config.get('offline_buffer', {})
    if offline_buffer.get('enabled', False):
        buffer_file = offline_buffer.get('buffer_file', 'Not set')
        max_size = offline_buffer.get('max_size', 10000)
        print(f"Offline Buffer: ✅ Enabled (file: {buffer_file}, max: {max_size})")
    else:
        print("Offline Buffer: ❌ Disabled")

def check_collection_config(config):
    """Check log collection configuration."""
    print("\n📝 Log Collection Configuration:")
    print("-" * 40)
    
    collection_config = config.get('collection', {})
    
    # Check journalctl
    journalctl = collection_config.get('journalctl', {})
    if journalctl.get('enabled', False):
        print("✅ Journalctl: Enabled")
        print(f"   Follow: {journalctl.get('follow', True)}")
        print(f"   Since: {journalctl.get('since', '1 hour ago')}")
        units = journalctl.get('units', [])
        if units:
            print(f"   Units: {', '.join(units)}")
        else:
            print("   Units: All units")
    else:
        print("❌ Journalctl: Disabled")
    
    # Check other collectors
    collectors = ['syslog', 'auth_logs', 'kernel_logs', 'application_logs', 'network_logs']
    for collector in collectors:
        collector_config = collection_config.get(collector, {})
        if collector_config.get('enabled', False):
            print(f"✅ {collector.replace('_', ' ').title()}: Enabled")
            paths = collector_config.get('paths', [])
            if paths:
                print(f"   Paths: {', '.join(paths)}")
        else:
            print(f"❌ {collector.replace('_', ' ').title()}: Disabled")

def check_output_config(config):
    """Check output configuration."""
    print("\n📤 Output Configuration:")
    print("-" * 30)
    
    output_config = config.get('output', {})
    
    # Check file output
    file_output = output_config.get('file', {})
    if file_output.get('enabled', False):
        path = file_output.get('path', 'Not set')
        print(f"✅ File Output: Enabled ({path})")
        
        # Check if file exists and is writable
        if os.path.exists(path):
            if os.access(path, os.W_OK):
                print(f"   File Status: ✅ Writable")
            else:
                print(f"   File Status: ❌ Not writable")
        else:
            # Check if directory exists and is writable
            dir_path = os.path.dirname(path)
            if os.path.exists(dir_path) and os.access(dir_path, os.W_OK):
                print(f"   Directory: ✅ Writable")
            else:
                print(f"   Directory: ❌ Not writable or doesn't exist")
    else:
        print("❌ File Output: Disabled")
    
    # Check console output
    console_output = output_config.get('console', {})
    if console_output.get('enabled', False):
        print("✅ Console Output: Enabled")
    else:
        print("❌ Console Output: Disabled")

def check_agent_status():
    """Check if agent is running."""
    print("\n🔄 Agent Status:")
    print("-" * 20)
    
    try:
        # Check systemd service
        result = subprocess.run(['systemctl', 'is-active', 'linux-log-agent'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Service Status: Active")
        else:
            print("❌ Service Status: Inactive")
        
        # Check if process is running
        result = subprocess.run(['pgrep', '-f', 'linux.*agent'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            pids = result.stdout.strip().split('\n')
            print(f"✅ Process: Running (PIDs: {', '.join(pids)})")
        else:
            print("❌ Process: Not running")
            
    except Exception as e:
        print(f"❌ Error checking status: {e}")

def check_log_files():
    """Check agent log files."""
    print("\n📋 Agent Log Files:")
    print("-" * 25)
    
    log_paths = [
        "/var/log/linux-log-agent/agent.log",
        "/var/log/linux-log-agent/standardized_logs.json",
        "/var/log/linux-log-agent/errors.log",
        "logs/agent.log",
        "logs/standardized_logs.json"
    ]
    
    for log_path in log_paths:
        if os.path.exists(log_path):
            try:
                stat = os.stat(log_path)
                size = stat.st_size
                print(f"✅ {log_path}: {size} bytes")
                
                # Show last few lines if it's a text log
                if log_path.endswith('.log'):
                    try:
                        with open(log_path, 'r') as f:
                            lines = f.readlines()
                            if lines:
                                print(f"   Last line: {lines[-1].strip()}")
                    except:
                        pass
                        
            except Exception as e:
                print(f"❌ {log_path}: Error reading - {e}")
        else:
            print(f"❌ {log_path}: Not found")

def check_recent_logs():
    """Check recent standardized logs."""
    print("\n📊 Recent Standardized Logs:")
    print("-" * 35)
    
    log_paths = [
        "/var/log/linux-log-agent/standardized_logs.json",
        "logs/standardized_logs.json"
    ]
    
    for log_path in log_paths:
        if os.path.exists(log_path):
            try:
                with open(log_path, 'r') as f:
                    lines = f.readlines()
                    
                if lines:
                    print(f"📄 File: {log_path}")
                    print(f"   Total lines: {len(lines)}")
                    
                    # Show last 3 logs
                    print("   Recent logs:")
                    for line in lines[-3:]:
                        try:
                            log_data = json.loads(line.strip())
                            timestamp = log_data.get('timestamp', 'Unknown')
                            source = log_data.get('source', 'Unknown')
                            message = log_data.get('message', 'No message')[:50]
                            print(f"     {timestamp} | {source} | {message}...")
                        except:
                            print(f"     Invalid JSON: {line.strip()[:50]}...")
                    break
                else:
                    print(f"📄 {log_path}: Empty file")
                    
            except Exception as e:
                print(f"❌ Error reading {log_path}: {e}")
    else:
        print("❌ No standardized log files found")

def main():
    print("🔍 Linux Agent Configuration Checker")
    print("=" * 50)
    
    # Load configuration
    config, config_path = load_config()
    if not config:
        sys.exit(1)
    
    # Run checks
    check_api_config(config)
    check_collection_config(config)
    check_output_config(config)
    check_agent_status()
    check_log_files()
    check_recent_logs()
    
    print("\n" + "=" * 50)
    print("🎯 Recommendations:")
    
    api_config = config.get('exlog_api', {})
    if not api_config.get('enabled', False):
        print("❗ Enable API in configuration")
    
    if not api_config.get('api_key') or api_config.get('api_key') == 'your-api-key-here':
        print("❗ Set correct API key in configuration")
    
    if 'localhost' in api_config.get('endpoint', ''):
        print("❗ Update API endpoint to dashboard server IP")
    
    collection_config = config.get('collection', {})
    enabled_collectors = sum(1 for collector in collection_config.values() 
                           if isinstance(collector, dict) and collector.get('enabled', False))
    
    if enabled_collectors == 0:
        print("❗ Enable at least one log collector")
    
    print("\n💡 Next steps:")
    print("1. Fix any configuration issues above")
    print("2. Restart the agent: sudo systemctl restart linux-log-agent")
    print("3. Monitor logs: sudo journalctl -u linux-log-agent -f")
    print("4. Test connection: python3 test-dashboard-connection.py")

if __name__ == "__main__":
    main()
