#!/usr/bin/env node

/**
 * Debug Log Failures Script
 * 
 * This script helps diagnose why some logs are failing validation
 * by testing various log formats and identifying issues.
 */

const axios = require('axios');

// Configuration
const API_BASE_URL = 'http://localhost:5000/api/v1';
const API_KEY = 'bb93493a1a62ace8d41d25cf233f67b5cb8dbdd709d5617c914c299bc5e4e9a0';

// Test different log formats to identify validation issues
const testLogs = [
  {
    name: "Valid Auth Log",
    log: {
      logId: "debug-auth-001",
      timestamp: new Date().toISOString(),
      source: "Auth",
      sourceType: "auth",
      host: "debug-test",
      logLevel: "info",
      message: "Valid auth log test"
    }
  },
  {
    name: "Valid Kernel Log",
    log: {
      logId: "debug-kernel-001",
      timestamp: new Date().toISOString(),
      source: "Kernel",
      sourceType: "kernel",
      host: "debug-test",
      logLevel: "warning",
      message: "Valid kernel log test"
    }
  },
  {
    name: "Valid Service Log",
    log: {
      logId: "debug-service-001",
      timestamp: new Date().toISOString(),
      source: "Service",
      sourceType: "service",
      host: "debug-test",
      logLevel: "info",
      message: "Valid service log test"
    }
  },
  {
    name: "Invalid Source",
    log: {
      logId: "debug-invalid-001",
      timestamp: new Date().toISOString(),
      source: "InvalidSource",
      sourceType: "auth",
      host: "debug-test",
      logLevel: "info",
      message: "Invalid source test"
    }
  },
  {
    name: "Invalid SourceType",
    log: {
      logId: "debug-invalid-002",
      timestamp: new Date().toISOString(),
      source: "Auth",
      sourceType: "invalid_type",
      host: "debug-test",
      logLevel: "info",
      message: "Invalid source type test"
    }
  },
  {
    name: "Missing LogId",
    log: {
      timestamp: new Date().toISOString(),
      source: "Auth",
      sourceType: "auth",
      host: "debug-test",
      logLevel: "info",
      message: "Missing logId test"
    }
  },
  {
    name: "Invalid Timestamp",
    log: {
      logId: "debug-timestamp-001",
      timestamp: "invalid-timestamp",
      source: "Auth",
      sourceType: "auth",
      host: "debug-test",
      logLevel: "info",
      message: "Invalid timestamp test"
    }
  },
  {
    name: "Invalid LogLevel",
    log: {
      logId: "debug-level-001",
      timestamp: new Date().toISOString(),
      source: "Auth",
      sourceType: "auth",
      host: "debug-test",
      logLevel: "invalid_level",
      message: "Invalid log level test"
    }
  },
  {
    name: "Linux Agent Format",
    log: {
      logId: "debug-linux-001",
      timestamp: new Date().toISOString(),
      source: "Auth",
      sourceType: "auth",
      host: "linux-server-01",
      logLevel: "info",
      message: "User john logged in via SSH from *************",
      additionalFields: {
        syslog_identifier: "sshd",
        pid: 1234,
        uid: 1000,
        systemd_unit: "ssh.service",
        collector: "journalctl"
      }
    }
  }
];

async function testLogValidation() {
  console.log('🧪 Testing Log Validation');
  console.log('=' * 50);
  
  const results = {
    passed: 0,
    failed: 0,
    errors: []
  };
  
  for (const test of testLogs) {
    try {
      console.log(`\n🔍 Testing: ${test.name}`);
      
      const response = await axios.post(
        `${API_BASE_URL}/logs`,
        { logs: [test.log] },
        {
          headers: {
            'Content-Type': 'application/json',
            'X-API-Key': API_KEY
          },
          timeout: 10000
        }
      );
      
      if (response.status === 201) {
        console.log(`✅ PASSED: ${test.name}`);
        results.passed++;
      } else {
        console.log(`❌ FAILED: ${test.name} - Status: ${response.status}`);
        results.failed++;
        results.errors.push({
          test: test.name,
          status: response.status,
          data: response.data
        });
      }
      
    } catch (error) {
      console.log(`❌ FAILED: ${test.name}`);
      
      if (error.response) {
        console.log(`   Status: ${error.response.status}`);
        console.log(`   Error: ${JSON.stringify(error.response.data, null, 2)}`);
        
        results.failed++;
        results.errors.push({
          test: test.name,
          status: error.response.status,
          error: error.response.data
        });
      } else {
        console.log(`   Network Error: ${error.message}`);
        results.failed++;
        results.errors.push({
          test: test.name,
          error: error.message
        });
      }
    }
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  // Summary
  console.log('\n' + '=' * 50);
  console.log('📊 Test Results Summary');
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`📈 Success Rate: ${((results.passed / testLogs.length) * 100).toFixed(1)}%`);
  
  if (results.errors.length > 0) {
    console.log('\n🔍 Detailed Error Analysis:');
    results.errors.forEach((error, index) => {
      console.log(`\n${index + 1}. ${error.test}:`);
      if (error.status) {
        console.log(`   HTTP Status: ${error.status}`);
      }
      if (error.error) {
        if (typeof error.error === 'object') {
          console.log(`   Details: ${JSON.stringify(error.error, null, 4)}`);
        } else {
          console.log(`   Error: ${error.error}`);
        }
      }
    });
  }
  
  // Recommendations
  console.log('\n💡 Recommendations:');
  
  const validationErrors = results.errors.filter(e => e.status === 400);
  const authErrors = results.errors.filter(e => e.status === 401);
  const serverErrors = results.errors.filter(e => e.status >= 500);
  
  if (validationErrors.length > 0) {
    console.log('📋 Validation Issues Found:');
    console.log('   - Check that all required fields are present');
    console.log('   - Verify source and sourceType values are in allowed enums');
    console.log('   - Ensure timestamp is in ISO8601 format');
    console.log('   - Confirm logLevel is one of: critical, error, warning, info, debug');
  }
  
  if (authErrors.length > 0) {
    console.log('🔐 Authentication Issues:');
    console.log('   - Verify API key is correct');
    console.log('   - Check X-API-Key header format');
  }
  
  if (serverErrors.length > 0) {
    console.log('🚨 Server Issues:');
    console.log('   - Check dashboard backend logs');
    console.log('   - Verify database connectivity');
    console.log('   - Check for backend configuration issues');
  }
  
  console.log('\n🔧 Next Steps:');
  console.log('1. Fix any validation issues identified above');
  console.log('2. Check Linux agent log format matches working examples');
  console.log('3. Monitor backend logs: sudo docker-compose logs -f backend');
  console.log('4. Verify database ingestion: node clear-logs-api.js stats');
}

async function checkCurrentValidationRules() {
  console.log('\n🔍 Checking Current Validation Rules');
  console.log('-' * 40);
  
  try {
    // Test with a minimal valid log to see current validation
    const testLog = {
      logId: "validation-check",
      timestamp: new Date().toISOString(),
      source: "System",
      sourceType: "event",
      host: "test",
      logLevel: "info",
      message: "Validation check"
    };
    
    const response = await axios.post(
      `${API_BASE_URL}/logs`,
      { logs: [testLog] },
      {
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': API_KEY
        }
      }
    );
    
    console.log('✅ Basic validation is working');
    console.log('📋 Supported categories should include:');
    console.log('   Sources: System, Application, Security, Network, Custom, Auth, Kernel, Service, Scheduler, Hardware, Systemd, Journal');
    console.log('   Types: event, application, security, network, audit, performance, auth, kernel, service, scheduler, hardware, systemd, syslog, journal');
    
  } catch (error) {
    console.log('❌ Basic validation failed');
    if (error.response) {
      console.log(`   Error: ${JSON.stringify(error.response.data, null, 2)}`);
    }
  }
}

async function main() {
  console.log('🔧 Dashboard Log Validation Debugger');
  console.log('=' * 50);
  
  await checkCurrentValidationRules();
  await testLogValidation();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testLogValidation };
