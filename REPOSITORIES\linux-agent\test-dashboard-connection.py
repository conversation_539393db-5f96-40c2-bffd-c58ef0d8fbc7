#!/usr/bin/env python3

"""
Dashboard Connection Test Script for Linux Agent

This script tests the connection between the Linux agent and the dashboard
to identify where logs might be getting lost.
"""

import requests
import json
import time
import sys
from datetime import datetime
import uuid

# Configuration - Update these with your actual values
DASHBOARD_URL = "http://your-dashboard-server-ip:5000"  # Update this
API_KEY = "bb93493a1a62ace8d41d25cf233f67b5cb8dbdd709d5617c914c299bc5e4e9a0"  # Update if different

def test_api_health():
    """Test if the dashboard API is reachable."""
    print("🔍 Testing Dashboard API Health...")
    try:
        response = requests.get(f"{DASHBOARD_URL}/api/v1/health", timeout=10)
        if response.status_code == 200:
            print(f"✅ Dashboard API is healthy: {response.json().get('message', 'OK')}")
            return True
        else:
            print(f"❌ Dashboard API returned status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot reach dashboard API: {e}")
        return False

def test_api_authentication():
    """Test API key authentication."""
    print("\n🔐 Testing API Key Authentication...")
    
    # Test with a simple log entry
    test_log = {
        "logs": [{
            "logId": f"test-{uuid.uuid4()}",
            "timestamp": datetime.now().isoformat(),
            "source": "System",
            "sourceType": "event",
            "host": "test-host",
            "logLevel": "info",
            "message": "Test log for authentication"
        }]
    }
    
    headers = {
        "Content-Type": "application/json",
        "X-API-Key": API_KEY
    }
    
    try:
        response = requests.post(
            f"{DASHBOARD_URL}/api/v1/logs",
            json=test_log,
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 201:
            print("✅ API key authentication successful")
            return True
        elif response.status_code == 401:
            print("❌ API key authentication failed - Invalid API key")
            return False
        else:
            print(f"❌ Unexpected response: {response.status_code} - {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False

def test_log_ingestion():
    """Test log ingestion with various categories."""
    print("\n📝 Testing Log Ingestion...")
    
    test_logs = [
        {
            "logId": f"test-auth-{uuid.uuid4()}",
            "timestamp": datetime.now().isoformat(),
            "source": "Auth",
            "sourceType": "auth",
            "host": "linux-test",
            "logLevel": "info",
            "message": "Test authentication log"
        },
        {
            "logId": f"test-kernel-{uuid.uuid4()}",
            "timestamp": datetime.now().isoformat(),
            "source": "Kernel",
            "sourceType": "kernel",
            "host": "linux-test",
            "logLevel": "warning",
            "message": "Test kernel log"
        },
        {
            "logId": f"test-system-{uuid.uuid4()}",
            "timestamp": datetime.now().isoformat(),
            "source": "System",
            "sourceType": "event",
            "host": "linux-test",
            "logLevel": "info",
            "message": "Test system log"
        }
    ]
    
    headers = {
        "Content-Type": "application/json",
        "X-API-Key": API_KEY
    }
    
    success_count = 0
    
    for i, log in enumerate(test_logs, 1):
        try:
            response = requests.post(
                f"{DASHBOARD_URL}/api/v1/logs",
                json={"logs": [log]},
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 201:
                print(f"✅ Test log {i} ({log['source']}) ingested successfully")
                success_count += 1
            else:
                print(f"❌ Test log {i} failed: {response.status_code} - {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Test log {i} request failed: {e}")
    
    print(f"\n📊 Ingestion test results: {success_count}/{len(test_logs)} successful")
    return success_count == len(test_logs)

def test_batch_ingestion():
    """Test batch log ingestion to simulate agent behavior."""
    print("\n📦 Testing Batch Log Ingestion...")
    
    # Create a batch of 10 logs
    batch_logs = []
    for i in range(10):
        batch_logs.append({
            "logId": f"batch-test-{i}-{uuid.uuid4()}",
            "timestamp": datetime.now().isoformat(),
            "source": "System",
            "sourceType": "event",
            "host": "linux-batch-test",
            "logLevel": "info",
            "message": f"Batch test log {i+1}"
        })
    
    headers = {
        "Content-Type": "application/json",
        "X-API-Key": API_KEY
    }
    
    try:
        start_time = time.time()
        response = requests.post(
            f"{DASHBOARD_URL}/api/v1/logs",
            json={"logs": batch_logs},
            headers=headers,
            timeout=30
        )
        end_time = time.time()
        
        if response.status_code == 201:
            print(f"✅ Batch of {len(batch_logs)} logs ingested successfully")
            print(f"⏱️  Time taken: {end_time - start_time:.2f} seconds")
            return True
        else:
            print(f"❌ Batch ingestion failed: {response.status_code} - {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Batch ingestion request failed: {e}")
        return False

def check_dashboard_logs():
    """Check recent logs in dashboard."""
    print("\n📋 Checking Recent Logs in Dashboard...")
    
    # Note: This would require authentication token, not API key
    # For now, we'll just provide instructions
    print("💡 To check logs in dashboard:")
    print(f"   1. Open browser to {DASHBOARD_URL.replace(':5000', ':8080')}")
    print("   2. <NAME_EMAIL> / Admin123!")
    print("   3. Go to Logs page")
    print("   4. Check for recent test logs")

def main():
    print("🧪 Linux Agent Dashboard Connection Test")
    print("=" * 50)
    
    # Update configuration prompt
    if "your-dashboard-server-ip" in DASHBOARD_URL:
        print("⚠️  Please update the DASHBOARD_URL in this script with your actual dashboard server IP")
        print("   Example: DASHBOARD_URL = 'http://*************:5000'")
        sys.exit(1)
    
    print(f"🎯 Target Dashboard: {DASHBOARD_URL}")
    print(f"🔑 API Key: {API_KEY[:20]}...")
    print()
    
    # Run tests
    tests_passed = 0
    total_tests = 4
    
    if test_api_health():
        tests_passed += 1
    
    if test_api_authentication():
        tests_passed += 1
    
    if test_log_ingestion():
        tests_passed += 1
    
    if test_batch_ingestion():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("✅ All tests passed! Connection to dashboard is working properly.")
        print("\n💡 If agent logs aren't appearing in dashboard, check:")
        print("   1. Agent configuration (API endpoint and key)")
        print("   2. Agent log collection settings")
        print("   3. Log filtering/categorization in agent")
        print("   4. Dashboard log retention settings")
    else:
        print("❌ Some tests failed. Check the errors above.")
        print("\n🔧 Common issues:")
        print("   1. Incorrect dashboard URL or port")
        print("   2. Invalid API key")
        print("   3. Network connectivity issues")
        print("   4. Dashboard not running or misconfigured")
    
    check_dashboard_logs()

if __name__ == "__main__":
    main()
