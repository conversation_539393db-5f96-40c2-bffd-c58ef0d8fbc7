#!/usr/bin/env node

/**
 * Clear Logs via Dashboard API
 * 
 * This script clears logs using the dashboard's API endpoints.
 * Requires authentication with admin credentials.
 */

const axios = require('axios');

// Configuration
const API_BASE_URL = 'http://localhost:5000/api/v1';
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_PASSWORD = 'Admin123!';

class APILogCleaner {
  constructor() {
    this.token = null;
  }

  async authenticate() {
    try {
      console.log('🔐 Authenticating with dashboard...');
      const response = await axios.post(`${API_BASE_URL}/auth/login`, {
        email: ADMIN_EMAIL,
        password: ADMIN_PASSWORD
      });
      
      this.token = response.data.token;
      console.log('✅ Authentication successful');
    } catch (error) {
      console.error('❌ Authentication failed:', error.response?.data?.message || error.message);
      throw error;
    }
  }

  async getLogCount(filters = {}) {
    try {
      const params = new URLSearchParams({
        limit: 1,
        ...filters
      });
      
      const response = await axios.get(`${API_BASE_URL}/logs?${params}`, {
        headers: { Authorization: `Bearer ${this.token}` }
      });
      
      return response.data.pagination?.totalCount || 0;
    } catch (error) {
      console.error('❌ Failed to get log count:', error.response?.data?.message || error.message);
      return 0;
    }
  }

  async showLogStatistics() {
    console.log('📊 Current Log Statistics (via API):');
    console.log('===================================');
    
    // Total count
    const totalCount = await this.getLogCount();
    console.log(`Total logs: ${totalCount.toLocaleString()}`);
    
    // Count by source
    const sources = ['System', 'Application', 'Security', 'Network', 'Custom', 
                    'Auth', 'Kernel', 'Service', 'Scheduler', 'Hardware', 'Systemd', 'Journal'];
    
    console.log('\nBy Source:');
    for (const source of sources) {
      const count = await this.getLogCount({ source });
      if (count > 0) {
        console.log(`  ${source}: ${count.toLocaleString()}`);
      }
    }
    
    // Count by log level
    const levels = ['critical', 'error', 'warning', 'info', 'debug'];
    
    console.log('\nBy Log Level:');
    for (const level of levels) {
      const count = await this.getLogCount({ logLevel: level });
      if (count > 0) {
        console.log(`  ${level}: ${count.toLocaleString()}`);
      }
    }
  }

  async clearLogsByFilter(description, filters) {
    console.log(`🗑️  ${description}...`);
    
    const count = await this.getLogCount(filters);
    console.log(`📊 Logs matching filter: ${count.toLocaleString()}`);
    
    if (count === 0) {
      console.log('ℹ️  No logs to delete');
      return;
    }

    // Note: This would require a DELETE endpoint in the API
    // For now, we'll show what would be deleted
    console.log('⚠️  API-based deletion not implemented yet.');
    console.log('💡 Use the MongoDB script method instead: node clear-logs.js');
    console.log(`📋 Filter that would be applied: ${JSON.stringify(filters)}`);
  }
}

async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  if (!command) {
    console.log('ExLog API Log Cleanup Script');
    console.log('============================');
    console.log('');
    console.log('Usage: node clear-logs-api.js <command> [options]');
    console.log('');
    console.log('Commands:');
    console.log('  stats                    - Show log statistics');
    console.log('  count-source <source>    - Count logs by source');
    console.log('  count-level <level>      - Count logs by level');
    console.log('  count-host <host>        - Count logs by host');
    console.log('');
    console.log('Examples:');
    console.log('  node clear-logs-api.js stats');
    console.log('  node clear-logs-api.js count-source Auth');
    console.log('  node clear-logs-api.js count-level debug');
    console.log('  node clear-logs-api.js count-host linux-server-01');
    console.log('');
    console.log('Note: For actual log deletion, use the MongoDB script:');
    console.log('      node clear-logs.js');
    return;
  }

  const cleaner = new APILogCleaner();
  
  try {
    await cleaner.authenticate();
    
    switch (command) {
      case 'stats':
        await cleaner.showLogStatistics();
        break;
        
      case 'count-source':
        const source = args[1];
        if (!source) {
          console.error('❌ Please specify a source');
          return;
        }
        const sourceCount = await cleaner.getLogCount({ source });
        console.log(`📊 Logs from source "${source}": ${sourceCount.toLocaleString()}`);
        break;
        
      case 'count-level':
        const level = args[1];
        if (!level) {
          console.error('❌ Please specify a log level');
          return;
        }
        const levelCount = await cleaner.getLogCount({ logLevel: level });
        console.log(`📊 Logs with level "${level}": ${levelCount.toLocaleString()}`);
        break;
        
      case 'count-host':
        const host = args[1];
        if (!host) {
          console.error('❌ Please specify a host');
          return;
        }
        const hostCount = await cleaner.getLogCount({ host });
        console.log(`📊 Logs from host "${host}": ${hostCount.toLocaleString()}`);
        break;
        
      default:
        console.error(`❌ Unknown command: ${command}`);
        console.log('Run without arguments to see usage information');
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = APILogCleaner;
