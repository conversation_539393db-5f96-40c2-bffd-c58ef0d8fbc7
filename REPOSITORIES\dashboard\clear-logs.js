#!/usr/bin/env node

/**
 * Log Cleanup Script for ExLog Dashboard
 * 
 * This script provides various options to clear logs from the MongoDB database.
 * Use with caution as this operation cannot be undone.
 */

const { MongoClient } = require('mongodb');

// Configuration
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/exlog';
const DB_NAME = 'exlog';
const COLLECTION_NAME = 'logs';

class LogCleaner {
  constructor() {
    this.client = null;
    this.db = null;
    this.collection = null;
  }

  async connect() {
    try {
      this.client = new MongoClient(MONGODB_URI);
      await this.client.connect();
      this.db = this.client.db(DB_NAME);
      this.collection = this.db.collection(COLLECTION_NAME);
      console.log('✅ Connected to MongoDB');
    } catch (error) {
      console.error('❌ Failed to connect to MongoDB:', error.message);
      process.exit(1);
    }
  }

  async disconnect() {
    if (this.client) {
      await this.client.close();
      console.log('✅ Disconnected from MongoDB');
    }
  }

  async getLogCount(filter = {}) {
    return await this.collection.countDocuments(filter);
  }

  async clearAllLogs() {
    console.log('🗑️  Clearing ALL logs...');
    
    const totalCount = await this.getLogCount();
    console.log(`📊 Total logs to delete: ${totalCount.toLocaleString()}`);
    
    if (totalCount === 0) {
      console.log('ℹ️  No logs to delete');
      return;
    }

    const result = await this.collection.deleteMany({});
    console.log(`✅ Deleted ${result.deletedCount.toLocaleString()} logs`);
  }

  async clearLogsByDateRange(startDate, endDate) {
    console.log(`🗑️  Clearing logs from ${startDate} to ${endDate}...`);
    
    const filter = {
      timestamp: {
        $gte: new Date(startDate),
        $lt: new Date(endDate)
      }
    };

    const count = await this.getLogCount(filter);
    console.log(`📊 Logs to delete: ${count.toLocaleString()}`);
    
    if (count === 0) {
      console.log('ℹ️  No logs found in date range');
      return;
    }

    const result = await this.collection.deleteMany(filter);
    console.log(`✅ Deleted ${result.deletedCount.toLocaleString()} logs`);
  }

  async clearOldLogs(daysOld) {
    console.log(`🗑️  Clearing logs older than ${daysOld} days...`);
    
    const cutoffDate = new Date(Date.now() - daysOld * 24 * 60 * 60 * 1000);
    const filter = {
      timestamp: { $lt: cutoffDate }
    };

    const count = await this.getLogCount(filter);
    console.log(`📊 Logs older than ${cutoffDate.toISOString()}: ${count.toLocaleString()}`);
    
    if (count === 0) {
      console.log('ℹ️  No old logs to delete');
      return;
    }

    const result = await this.collection.deleteMany(filter);
    console.log(`✅ Deleted ${result.deletedCount.toLocaleString()} old logs`);
  }

  async clearLogsBySource(sources) {
    console.log(`🗑️  Clearing logs from sources: ${sources.join(', ')}...`);
    
    const filter = {
      source: { $in: sources }
    };

    const count = await this.getLogCount(filter);
    console.log(`📊 Logs to delete: ${count.toLocaleString()}`);
    
    if (count === 0) {
      console.log('ℹ️  No logs found for specified sources');
      return;
    }

    const result = await this.collection.deleteMany(filter);
    console.log(`✅ Deleted ${result.deletedCount.toLocaleString()} logs`);
  }

  async clearLogsByLogLevel(logLevels) {
    console.log(`🗑️  Clearing logs with levels: ${logLevels.join(', ')}...`);
    
    const filter = {
      logLevel: { $in: logLevels }
    };

    const count = await this.getLogCount(filter);
    console.log(`📊 Logs to delete: ${count.toLocaleString()}`);
    
    if (count === 0) {
      console.log('ℹ️  No logs found for specified log levels');
      return;
    }

    const result = await this.collection.deleteMany(filter);
    console.log(`✅ Deleted ${result.deletedCount.toLocaleString()} logs`);
  }

  async showLogStatistics() {
    console.log('📊 Current Log Statistics:');
    console.log('========================');
    
    const totalCount = await this.getLogCount();
    console.log(`Total logs: ${totalCount.toLocaleString()}`);
    
    // Count by source
    const sourceStats = await this.collection.aggregate([
      { $group: { _id: '$source', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]).toArray();
    
    console.log('\nBy Source:');
    sourceStats.forEach(stat => {
      console.log(`  ${stat._id}: ${stat.count.toLocaleString()}`);
    });
    
    // Count by log level
    const levelStats = await this.collection.aggregate([
      { $group: { _id: '$logLevel', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]).toArray();
    
    console.log('\nBy Log Level:');
    levelStats.forEach(stat => {
      console.log(`  ${stat._id}: ${stat.count.toLocaleString()}`);
    });
    
    // Date range
    const dateRange = await this.collection.aggregate([
      {
        $group: {
          _id: null,
          oldest: { $min: '$timestamp' },
          newest: { $max: '$timestamp' }
        }
      }
    ]).toArray();
    
    if (dateRange.length > 0 && dateRange[0].oldest) {
      console.log('\nDate Range:');
      console.log(`  Oldest: ${dateRange[0].oldest.toISOString()}`);
      console.log(`  Newest: ${dateRange[0].newest.toISOString()}`);
    }
  }
}

async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  if (!command) {
    console.log('ExLog Log Cleanup Script');
    console.log('========================');
    console.log('');
    console.log('Usage: node clear-logs.js <command> [options]');
    console.log('');
    console.log('Commands:');
    console.log('  stats                           - Show log statistics');
    console.log('  clear-all                       - Clear ALL logs (use with caution!)');
    console.log('  clear-old <days>               - Clear logs older than X days');
    console.log('  clear-date <start> <end>       - Clear logs in date range (YYYY-MM-DD)');
    console.log('  clear-source <source1,source2> - Clear logs from specific sources');
    console.log('  clear-level <level1,level2>    - Clear logs with specific levels');
    console.log('  clear-linux                    - Clear all Linux agent logs');
    console.log('  clear-windows                  - Clear all Windows agent logs');
    console.log('');
    console.log('Examples:');
    console.log('  node clear-logs.js stats');
    console.log('  node clear-logs.js clear-old 7');
    console.log('  node clear-logs.js clear-date 2025-06-01 2025-06-20');
    console.log('  node clear-logs.js clear-source Auth,Kernel');
    console.log('  node clear-logs.js clear-level debug,info');
    console.log('  node clear-logs.js clear-linux');
    console.log('');
    console.log('⚠️  WARNING: Deletion operations cannot be undone!');
    return;
  }

  const cleaner = new LogCleaner();
  await cleaner.connect();

  try {
    switch (command) {
      case 'stats':
        await cleaner.showLogStatistics();
        break;
        
      case 'clear-all':
        console.log('⚠️  WARNING: This will delete ALL logs!');
        console.log('Press Ctrl+C to cancel, or wait 5 seconds to continue...');
        await new Promise(resolve => setTimeout(resolve, 5000));
        await cleaner.clearAllLogs();
        break;
        
      case 'clear-old':
        const days = parseInt(args[1]);
        if (!days || days < 1) {
          console.error('❌ Please specify number of days (e.g., clear-old 7)');
          return;
        }
        await cleaner.clearOldLogs(days);
        break;
        
      case 'clear-date':
        const startDate = args[1];
        const endDate = args[2];
        if (!startDate || !endDate) {
          console.error('❌ Please specify start and end dates (e.g., clear-date 2025-06-01 2025-06-20)');
          return;
        }
        await cleaner.clearLogsByDateRange(startDate, endDate);
        break;
        
      case 'clear-source':
        const sources = args[1] ? args[1].split(',') : [];
        if (sources.length === 0) {
          console.error('❌ Please specify sources (e.g., clear-source Auth,Kernel)');
          return;
        }
        await cleaner.clearLogsBySource(sources);
        break;
        
      case 'clear-level':
        const levels = args[1] ? args[1].split(',') : [];
        if (levels.length === 0) {
          console.error('❌ Please specify log levels (e.g., clear-level debug,info)');
          return;
        }
        await cleaner.clearLogsByLogLevel(levels);
        break;
        
      case 'clear-linux':
        const linuxSources = ['Auth', 'Kernel', 'Service', 'Scheduler', 'Hardware', 'Systemd', 'Journal'];
        await cleaner.clearLogsBySource(linuxSources);
        break;
        
      case 'clear-windows':
        const windowsSources = ['System', 'Application', 'Security', 'Network', 'Custom'];
        await cleaner.clearLogsBySource(windowsSources);
        break;
        
      default:
        console.error(`❌ Unknown command: ${command}`);
        console.log('Run without arguments to see usage information');
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await cleaner.disconnect();
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = LogCleaner;
